function TextareaField(
    {id, name, value, onChange, error}: {
        id: string,
        name: string,
        value: string,
        onChange: (value: string) => void,
        error: string
    }
) {
    return (
        <div>
            <textarea
                id={id}
                name={name}
                value={value}
                onChange={event => onChange(event.target.value)}
                className={error ? 'error' : ''}
            />
            {error && <span className="error-message">{error}</span>}
        </div>
    )
}

export default TextareaField;
