/* Main App Layout */
#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 1rem;
  min-height: 100vh;
}

/* Schema Input and Preview Section */
.schema-input-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.schema-input,
.schema-preview {
  display: flex;
  flex-direction: column;
}

.schema-input textarea,
.schema-preview textarea {
  flex: 1;
  min-height: 200px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.4;
  border-radius: 6px;
  padding: 1rem;
}

.schema-preview textarea {
  cursor: not-allowed;
  background-color: #f8f9fa;
  color: #6c757d;
}

/* Form Section */
form {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

form h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: inherit;
}

/* Form Field Container */
form > div {
  margin-bottom: 1.5rem;
}

form > div:last-of-type {
  margin-bottom: 2rem;
}

/* Submit Button */
form button[type="submit"] {
  width: 100%;
  margin-top: 1rem;
  padding: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
}

/* Submitted Data Display */
.submitted-data {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: rgba(0, 255, 0, 0.05);
  border: 1px solid rgba(0, 255, 0, 0.2);
  border-radius: 8px;
}

.submitted-data h3 {
  margin-top: 0;
  color: #22c55e;
}

.submitted-data textarea {
  background-color: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 255, 0, 0.3);
  color: inherit;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  min-height: 150px;
}

/* Tablet (768px - 1024px) */
@media (max-width: 1024px) {
  #root {
    padding: 1rem;
  }

  .schema-input-container {
    gap: 1rem;
  }

  form {
    padding: 1.5rem;
  }
}

/* Mobile (320px - 768px) */
@media (max-width: 768px) {
  #root {
    padding: 0.75rem;
  }

  .schema-input-container {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .schema-input textarea,
  .schema-preview textarea {
    min-height: 150px;
    font-size: 0.8rem;
    padding: 0.75rem;
  }

  form {
    padding: 1rem;
    margin: 0;
    border-radius: 8px;
  }

  form h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  form > div {
    margin-bottom: 1.25rem;
  }

  form button[type="submit"] {
    padding: 0.875rem;
    font-size: 1rem;
  }

  div[data-testid="submitted-data"] {
    padding: 1rem;
  }

  div[data-testid="submitted-data"] textarea {
    min-height: 120px;
    font-size: 0.8rem;
  }
}

/* Small Mobile (320px - 480px) */
@media (max-width: 480px) {
  #root {
    padding: 0.5rem;
  }

  .schema-input textarea,
  .schema-preview textarea {
    min-height: 120px;
    font-size: 0.75rem;
    padding: 0.5rem;
  }

  form {
    padding: 0.75rem;
  }

  form h2 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }

  form > div {
    margin-bottom: 1rem;
  }

  label {
    font-size: 0.9rem;
  }

  input,
  textarea,
  select {
    padding: 0.625rem;
    font-size: 0.9rem;
  }

  form button[type="submit"] {
    padding: 0.75rem;
    font-size: 0.95rem;
  }
}

/* Light mode adjustments */
@media (prefers-color-scheme: light) {
  form {
    background-color: rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .schema-preview textarea {
    background-color: #f1f3f4;
    color: #5f6368;
  }

  div[data-testid="submitted-data"] {
    background-color: rgba(34, 197, 94, 0.05);
    border: 1px solid rgba(34, 197, 94, 0.2);
  }

  div[data-testid="submitted-data"] textarea {
    background-color: rgba(34, 197, 94, 0.05);
    border: 1px solid rgba(34, 197, 94, 0.3);
  }
}